import 'dart:convert';

/// 日志条目的数据模型。
class LogEntry {
  final String raw;
  final String timestamp;
  final String status;
  final String method;
  final String uri;
  final String domain;
  final String clientIp;
  final String responseTime;
  final String requestTime;
  final String userAgent;
  final String bodyBytes;
  final bool isJson;
  final Map<String, dynamic> parsed;
  final Map<String, List<String>> highlights;
  final String level;       // 例如: INFO, ERROR
  final String className;   // 例如: cn.xdf.work.wechat.magic.config.aop.OperationLogAspect
  final Map<String, String> parsedMessage; // 保存从 message 解析出的键值对

  // 私有构造函数，强制使用工厂构造函数
  const LogEntry._({
    required this.raw,
    required this.timestamp,
    required this.status,
    required this.method,
    required this.uri,
    required this.domain,
    required this.clientIp,
    required this.responseTime,
    required this.requestTime,
    required this.userAgent,
    required this.bodyBytes,
    required this.parsed,
    required this.highlights,
    required this.level,
    required this.className,
    required this.parsedMessage,
  }) : isJson = true;

  // 新的主要工厂构造函数
  factory LogEntry.fromJson(Map<String, dynamic> hit) {
    final source = hit['_source'] as Map<String, dynamic>;
    final highlightData = hit['highlight'] as Map<String, dynamic>? ?? {};

    final highlights = highlightData.map(
      (key, value) => MapEntry(key, (value as List).map((e) => e.toString()).toList()),
    );
    
    const encoder = JsonEncoder.withIndent('  ');
    final prettyRawString = encoder.convert(source);

    // 解析 message
    final message = source['message'] as String? ?? '';
    final Map<String, String> parsedFromMsg = _parseMessage(message);

    // 将解析出的字段与 source 合并，用于搜索和高亮
    final Map<String, dynamic> combinedParsed = Map.from(source);
    parsedFromMsg.forEach((key, value) {
      combinedParsed.putIfAbsent(key, () => value);
    });

    // 安全获取字段并确保所有值都转换为字符串
    String safeGetString(String key) {
      final value = source[key];
      if (value == null) return '';
      return value.toString();
    }

    // 特殊处理时间戳字段
    String getTimestamp() {
      final timestampValue = source['@timestamp'];
      if (timestampValue == null) return '';

      // 如果是数字（毫秒时间戳），转换为 ISO 8601 格式
      if (timestampValue is num) {
        try {
          final dateTime = DateTime.fromMillisecondsSinceEpoch(timestampValue.toInt());
          return dateTime.toIso8601String();
        } catch (e) {
          print("Error converting timestamp: $e");
          return timestampValue.toString();
        }
      }

      // 如果已经是字符串，直接返回
      return timestampValue.toString();
    }

    return LogEntry._(
      raw: prettyRawString,
      parsed: combinedParsed, // 使用合并后的 map
      highlights: highlights,
      parsedMessage: parsedFromMsg, // 将解析结果单独保存，供 UI 使用
      timestamp: getTimestamp(),
      status: safeGetString('status'),
      method: safeGetString('method'),
      uri: source['uri'] != null ? safeGetString('uri') : safeGetString('request_uri'),
      domain: safeGetString('domain'),
      clientIp: safeGetString('client_ip'),
      responseTime: safeGetString('upstream_response_time'),
      requestTime: safeGetString('request_time'),
      userAgent: safeGetString('http_user_agent'),
      bodyBytes: safeGetString('body_bytes_sent'),
      level: safeGetString('level'),
      className: safeGetString('class'),
    );
  }

  // 从解析后的JSON中安全地获取顶层message字段
  String get message {
    return parsed['message'] as String? ?? '';
  }

  // 之前的 parsedFromMessage getter 现在是一个私有静态方法
  static Map<String, String> _parseMessage(String message) {
    final Map<String, String> result = {};
    if (message.isEmpty) return result;

    try {
      // 1. 提取 email (如果存在)
      final emailMatch = RegExp(r'email=([\w.-]+@[\w.-]+)').firstMatch(message);
      if (emailMatch != null) {
        result['email'] = emailMatch.group(1)!;
      }
      
      // 2. 提取 data={...} JSON 部分
      // 找到 "data={" 的起始位置
      final jsonPrefix = 'data=';
      final jsonStartIndex = message.indexOf(jsonPrefix);

      if (jsonStartIndex != -1) {
          // 找到 JSON 字符串的真实起始位置
          final actualJsonStartIndex = jsonStartIndex + jsonPrefix.length;
          final jsonString = message.substring(actualJsonStartIndex);
          
          // 3. 解码 JSON
          final decodedJson = json.decode(jsonString);

          // 确保 'data' 和 'device' 字段存在
          if (decodedJson case {'data': {'device': Map device, 'taskId': String taskId}}) {
            // 4. 将 taskId 添加到结果中
            result['taskId'] = taskId;

            // 5. 将 device Map 中的所有键值对添加到结果中
            device.forEach((key, value) {
              result[key.toString()] = value.toString();
            });
          }
      }
    } catch (e) {
      // 如果解析失败，打印错误但不要让整个应用崩溃
      print("Error parsing message content: $e");
      // 即使部分解析失败，也可能已经提取出了一些信息（如 email），所以仍然返回 result
    }

    return result;
  }
}